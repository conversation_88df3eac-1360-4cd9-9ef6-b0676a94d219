{"name": "mobile-app-v3", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo build", "test": "jest --passWithNoTests", "test:watch": "jest --watch --passWithNoTests", "test:coverage": "jest --coverage --passWithNoTests", "test:unit": "jest --testPathPattern=\"(components|services|utils)\" --passWithNoTests", "test:features": "jest --testPathPattern=\"features\" --passWithNoTests", "test:integration": "jest --testPathPattern=\"integration\" --passWithNoTests", "test:e2e": "jest --testPathPattern=\"e2e\" --passWithNoTests", "test:islamic": "jest --testPathPattern=\"islamic\" --passWithNoTests", "test:all": "npm run test:unit && npm run test:features && npm run test:integration", "test:ci": "jest --coverage --watchAll=false --ci --passWithNoTests", "test:setup": "mkdir -p __tests__/{components,services,utils,features,integration,e2e}", "lint": "eslint src", "lint:fix": "eslint src --fix", "type-check": "tsc --noEmit", "clean": "rm -rf node_modules && rm -rf .expo && rm -rf dist", "reset": "npm run clean && npm install"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^7.1.6", "@react-navigation/stack": "^6.3.0", "expo": "~53.0.11", "expo-av": "~15.1.6", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-linking": "~7.1.5", "expo-router": "~5.1.0", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.8", "expo-web-browser": "~14.1.6", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.3", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "~0.20.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-syntax-jsx": "^7.27.1", "@babel/plugin-transform-react-jsx": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@testing-library/jest-native": "^5.4.0", "@testing-library/react-native": "^12.0.0", "@types/jsdom": "^21.1.7", "@types/react": "~19.0.10", "@types/react-native": "~0.72.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.0.0", "eslint-config-expo": "~9.2.0", "jest": "^29.2.1", "jest-expo": "~53.0.7", "jest-html-reporters": "^3.1.0", "jest-junit": "^16.0.0", "jsdom": "^26.1.0", "react-test-renderer": "19.0.0", "typescript": "~5.8.3"}, "private": true}