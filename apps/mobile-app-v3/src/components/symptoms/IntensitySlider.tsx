import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Easing,
  AccessibilityInfo,
  TouchableOpacity,
} from 'react-native';
import Slider from '@react-native-community/slider';
import { MaterialIcons } from '@expo/vector-icons';
import { colors } from "../../constants/Colors";

interface IntensitySliderProps {
  /**
   * Title of the slider
   */
  title: string;

  /**
   * Current intensity value (1-10)
   */
  value: number;

  /**
   * Called when the slider value changes
   */
  onValueChange: (value: number) => void;

  /**
   * Optional custom styles
   */
  style?: object;

  /**
   * Optional custom color
   */
  color?: string;

  /**
   * Optional minimum value (default: 1)
   */
  minValue?: number;

  /**
   * Optional maximum value (default: 10)
   */
  maxValue?: number;

  /**
   * Optional step value (default: 1)
   */
  step?: number;

  /**
   * Optional help text to display
   */
  helpText?: string;
}

/**
 * A slider component for selecting symptom intensity
 */
export default function IntensitySlider({
  title,
  value,
  onValueChange,
  style,
  color,
  minValue = 1,
  maxValue = 10,
  step = 1,
  helpText = 'Rate how much this affects you from 1 (minimal) to 10 (severe).',
}: IntensitySliderProps) {
  const styles = createStyles(colors);

  // Set default color if not provided
  const effectiveColor = color || colors.primary;
  // Animation value for intensity label
  const [animValue] = useState(new Animated.Value(0));

  // State for help text visibility
  const [isHelpVisible, setIsHelpVisible] = useState(false);

  // State for screen reader availability
  const [screenReaderEnabled, setScreenReaderEnabled] = useState(false);

  // Ref to track previous value for animations
  const prevValueRef = useRef(value);

  // Check if screen reader is enabled
  useEffect(() => {
    const checkScreenReader = async () => {
      const isEnabled = await AccessibilityInfo.isScreenReaderEnabled();
      setScreenReaderEnabled(isEnabled);
    };

    checkScreenReader();

    // Listen for screen reader changes
    const subscription = AccessibilityInfo.addEventListener(
      'screenReaderChanged',
      setScreenReaderEnabled
    );

    return () => {
      // Clean up subscription
      subscription.remove();
    };
  }, []);

  // Animate intensity label when value changes
  useEffect(() => {
    if (value !== prevValueRef.current) {
      Animated.sequence([
        Animated.timing(animValue, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
          easing: Easing.out(Easing.cubic),
        }),
        Animated.timing(animValue, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
          easing: Easing.in(Easing.cubic),
        }),
      ]).start();

      prevValueRef.current = value;
    }
  }, [value, animValue]);

  // Get intensity label based on value
  const getIntensityLabel = () => {
    if (value <= maxValue * 0.2) return 'Minimal';
    if (value <= maxValue * 0.4) return 'Mild';
    if (value <= maxValue * 0.6) return 'Moderate';
    if (value <= maxValue * 0.8) return 'Significant';
    return 'Severe';
  };

  // Get color based on intensity
  const getIntensityColor = () => {
    if (value <= maxValue * 0.2) return '#4CAF50'; // Green
    if (value <= maxValue * 0.4) return '#8BC34A'; // Light Green
    if (value <= maxValue * 0.6) return '#FFC107'; // Yellow
    if (value <= maxValue * 0.8) return '#FF9800'; // Orange
    return '#F44336'; // Red
  };

  // Toggle help text visibility
  const toggleHelp = () => {
    setIsHelpVisible(!isHelpVisible);
  };

  // Handle value increment/decrement for accessibility
  const incrementValue = () => {
    if (value < maxValue) {
      onValueChange(Math.min(value + step, maxValue));
    }
  };

  const decrementValue = () => {
    if (value > minValue) {
      onValueChange(Math.max(value - step, minValue));
    }
  };

  return (
    <View
      style={[styles.container, style]}
      accessible={true}
      accessibilityLabel={`${title} intensity slider, current value is ${value} out of ${maxValue}, which is ${getIntensityLabel()}`}
      accessibilityRole="adjustable"
      accessibilityActions={[
        { name: 'increment', label: 'Increase intensity' },
        { name: 'decrement', label: 'Decrease intensity' },
      ]}
      accessibilityHint="Adjust the slider to rate the intensity"
      onAccessibilityAction={(event) => {
        switch (event.nativeEvent.actionName) {
          case 'increment':
            incrementValue();
            break;
          case 'decrement':
            decrementValue();
            break;
        }
      }}
    >
      <View style={styles.titleRow}>
        <Text style={styles.title}>{title}</Text>
        <MaterialIcons
          name={isHelpVisible ? 'help' : 'help-outline'}
          size={20}
          onPress={toggleHelp}
          accessibilityLabel={
            isHelpVisible ? 'Hide help text' : 'Show help text'
          }
          accessibilityRole="button"
        />
      </View>

      {isHelpVisible && <Text style={styles.helpText}>{helpText}</Text>}

      <View style={styles.sliderContainer}>
        {screenReaderEnabled ? (
          // Accessible buttons for screen readers
          <View style={styles.accessibleControls}>
            <TouchableOpacity
              style={styles.accessibleButton}
              onPress={decrementValue}
              accessibilityLabel={`Decrease intensity to ${value - step}`}
              accessibilityRole="button"
              disabled={value <= minValue}
            >
              <MaterialIcons name="remove" size={24} />
            </TouchableOpacity>

            <View style={styles.accessibleValueContainer}>
              <Text style={styles.accessibleValue}>{value}</Text>
              <Text style={styles.accessibleLabel}>{getIntensityLabel()}</Text>
            </View>

            <TouchableOpacity
              style={styles.accessibleButton}
              onPress={incrementValue}
              accessibilityLabel={`Increase intensity to ${value + step}`}
              accessibilityRole="button"
              disabled={value >= maxValue}
            >
              <MaterialIcons name="add" size={24} />
            </TouchableOpacity>
          </View>
        ) : (
          // Regular slider for non-screen reader users
          <Slider
            style={styles.slider}
            value={value}
            onValueChange={onValueChange}
            minimumValue={minValue}
            maximumValue={maxValue}
            step={step}
            minimumTrackTintColor={getIntensityColor()}
            maximumTrackTintColor="#DDDDDD"
            thumbTintColor={getIntensityColor()}
            accessible={true}
            accessibilityLabel={`Intensity slider, current value is ${value}`}
          />
        )}

        <View style={styles.valuesRow}>
          <Text style={styles.valueLabel}>{minValue}</Text>
          <Animated.View
            style={[
              styles.currentValueContainer,
              {
                backgroundColor: getIntensityColor(),
                transform: [
                  {
                    scale: animValue.interpolate({
                      inputRange: [0, 0.5, 1],
                      outputRange: [1, 1.1, 1],
                    }),
                  },
                ],
              },
            ]}
          >
            <Text style={styles.currentValue}>{value}</Text>
            <Text style={styles.intensityLabel}>{getIntensityLabel()}</Text>
          </Animated.View>
          <Text style={styles.valueLabel}>{maxValue}</Text>
        </View>
      </View>
    </View>
  );
}

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      marginVertical: 12,
      paddingHorizontal: 8,
    },
    titleRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 4,
    },
    title: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text,
    },
    helpText: {
      fontSize: 12,
      color: colors.textSecondary,
      marginBottom: 8,
      fontStyle: 'italic',
    },
    sliderContainer: {
      marginTop: 8,
    },
    slider: {
      height: 40,
      width: '100%',
    },
    valuesRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 8,
      marginTop: -8,
    },
    valueLabel: {
      fontSize: 12,
      color: colors.textSecondary,
    },
    currentValueContainer: {
      paddingVertical: 4,
      paddingHorizontal: 12,
      borderRadius: 16,
      alignItems: 'center',
    },
    currentValue: {
      fontSize: 16,
      fontWeight: 'bold',
      color: 'white',
    },
    intensityLabel: {
      fontSize: 12,
      color: 'white',
      marginTop: -2,
    },
    accessibleControls: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginVertical: 10,
    },
    accessibleButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: '#F0F0F0',
      justifyContent: 'center',
      alignItems: 'center',
    },
    accessibleValueContainer: {
      alignItems: 'center',
    },
    accessibleValue: {
      fontSize: 20,
      fontWeight: 'bold',
      color: colors.text,
    },
    accessibleLabel: {
      fontSize: 14,
      color: colors.textSecondary,
    },
  });
