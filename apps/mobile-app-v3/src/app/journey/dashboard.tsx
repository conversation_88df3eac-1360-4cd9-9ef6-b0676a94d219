/**
 * Journey Dashboard Screen for Feature 2: Personalized Healing Journeys
 * Main dashboard for active healing journey
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';

// Import services
import journeyService from '../../services/api/JourneyService';
import { authService } from '../../services/auth.service';
import { Journey } from '../../services/api/types';

// Import components
import { ProgressCircle } from '../../components/ProgressCircle';
import { DailyPracticeCard } from '../../components/DailyPracticeCard';
import { JourneyStatsCard } from '../../components/JourneyStatsCard';
import { CommunityCard } from '../../components/CommunityCard';

// Extended Journey interface for UI-specific properties
interface JourneyWithUI extends Journey {
  totalProgress?: number;
  configuration?: {
    duration: number;
    dailyTimeCommitment: number;
    primaryLayer: string;
    communityIntegration: boolean;
  };
  days?: Array<{
    dayNumber: number;
    theme: string;
    practices: Array<{
      id: string;
      title: string;
      duration: number;
      type: string;
    }>;
  }>;
}

const { width, height } = Dimensions.get('window');

export default function JourneyDashboard() {
  const [journey, setJourney] = useState<JourneyWithUI | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [todaysPractices, setTodaysPractices] = useState<any[]>([]);
  const [stats, setStats] = useState({
    streak: 0,
    completedDays: 0,
    averageRating: 0,
  });

  useEffect(() => {
    loadJourneyData();
  }, []);

  const loadJourneyData = async () => {
    try {
      setIsLoading(true);

      // Get current journey
      const currentJourney = await journeyService.getCurrentJourney();

      if (!currentJourney) {
        // No active journey, redirect to journey creation
        router.replace('/journey/create' as any);
        return;
      }

      // Transform API journey to UI journey
      const journeyWithUI: JourneyWithUI = {
        ...currentJourney,
        totalProgress: currentJourney.progress || 0,
        configuration: {
          duration: currentJourney.total_days || currentJourney.duration || 7,
          dailyTimeCommitment: currentJourney.dailyCommitmentMinutes || 30,
          primaryLayer: currentJourney.focus_layers?.[0] || 'qalb',
          communityIntegration: true,
        },
        days: [], // Will be populated from journey modules if available
      };

      setJourney(journeyWithUI);

      // Get today's practices (mock for now since API doesn't have days structure)
      setTodaysPractices([
        {
          id: 'practice-1',
          title: 'Morning Dhikr',
          duration: 15,
          type: 'dhikr',
        },
        {
          id: 'practice-2',
          title: 'Reflection',
          duration: 10,
          type: 'reflection',
        },
      ]);

      // Load journey stats
      await loadJourneyStats();
    } catch (error) {
      console.error('Error loading journey data:', error);
      Alert.alert('Error', 'Failed to load journey data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const loadJourneyStats = async () => {
    try {
      const analytics = await journeyService.getJourneyAnalytics();
      setStats({
        streak: analytics.streakDays || 0,
        completedDays: analytics.completedDays || 0,
        averageRating: analytics.averageRating || 0,
      });
    } catch (error) {
      console.error('Error loading journey stats:', error);
    }
  };

  const handlePracticePress = (practice: any) => {
    router.push({
      pathname: '/journey/practice' as any,
      params: {
        practiceId: practice.id,
        journeyId: journey?.id,
        dayNumber: journey?.current_day,
      },
    } as any);
  };

  const handleProgressPress = () => {
    router.push({
      pathname: '/journey/progress' as any,
      params: { journeyId: journey?.id },
    } as any);
  };

  const handleCommunityPress = () => {
    router.push({
      pathname: '/journey/community' as any,
      params: { journeyId: journey?.id },
    } as any);
  };

  const handleReflectionPress = () => {
    router.push({
      pathname: '/journey/reflection' as any,
      params: {
        journeyId: journey?.id,
        dayNumber: journey?.current_day,
      },
    });
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <LinearGradient
          colors={['#1a365d', '#2d5a87', '#4a90a4']}
          style={styles.loadingContainer}
        >
          <ActivityIndicator size="large" color="#ffffff" />
          <Text style={styles.loadingText}>Loading your journey...</Text>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  if (!journey) {
    return (
      <SafeAreaView style={styles.container}>
        <LinearGradient
          colors={['#1a365d', '#2d5a87', '#4a90a4']}
          style={styles.errorContainer}
        >
          <Ionicons name="alert-circle" size={64} color="#ffffff" />
          <Text style={styles.errorTitle}>No Active Journey</Text>
          <Text style={styles.errorText}>
            You don't have an active healing journey. Let's create one for you.
          </Text>
          <TouchableOpacity
            style={styles.createButton}
            onPress={() => router.replace('/journey/create' as any)}
          >
            <Text style={styles.createButtonText}>Create Journey</Text>
          </TouchableOpacity>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#1a365d', '#2d5a87', '#4a90a4']}
        style={styles.gradient}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={styles.greeting}>Assalamu Alaikum</Text>
            <Text style={styles.journeyTitle}>
              {journey.title || 'Your Healing Journey'}
            </Text>
            <Text style={styles.journeySubtitle}>
              Day {journey.current_day} of{' '}
              {journey.configuration?.duration || journey.total_days}
            </Text>
          </View>

          <TouchableOpacity
            style={styles.settingsButton}
            onPress={() => router.push('/journey/settings' as any)}
          >
            <Ionicons name="settings-outline" size={24} color="#ffffff" />
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.contentContainer}
        >
          {/* Progress Overview */}
          <View style={styles.progressSection}>
            <TouchableOpacity
              style={styles.progressCard}
              onPress={handleProgressPress}
            >
              <View style={styles.progressContent}>
                <ProgressCircle
                  progress={journey.totalProgress || journey.progress || 0}
                  size={80}
                  strokeWidth={8}
                  color="#4a90a4"
                />
                <View style={styles.progressInfo}>
                  <Text style={styles.progressTitle}>Journey Progress</Text>
                  <Text style={styles.progressPercentage}>
                    {Math.round(journey.totalProgress || journey.progress || 0)}
                    %
                  </Text>
                  <Text style={styles.progressSubtext}>
                    {stats.completedDays} days completed
                  </Text>
                </View>
              </View>
              <Ionicons
                name="chevron-forward"
                size={20}
                color="rgba(255, 255, 255, 0.6)"
              />
            </TouchableOpacity>
          </View>

          {/* Journey Stats */}
          <View style={styles.statsSection}>
            <Text style={styles.sectionTitle}>Your Progress</Text>
            <View style={styles.statsGrid}>
              <JourneyStatsCard
                icon="flame"
                title="Current Streak"
                value={`${stats.streak} days`}
                color="#ff6b35"
              />
              <JourneyStatsCard
                icon="star"
                title="Average Rating"
                value={stats.averageRating.toFixed(1)}
                color="#ffd700"
              />
              <JourneyStatsCard
                icon="heart"
                title="Focus Layer"
                value={
                  journey.configuration?.primaryLayer ||
                  journey.focus_layers?.[0] ||
                  'qalb'
                }
                color="#e74c3c"
              />
            </View>
          </View>

          {/* Today's Practices */}
          <View style={styles.practicesSection}>
            <Text style={styles.sectionTitle}>Today's Practices</Text>
            {todaysPractices.length > 0 ? (
              todaysPractices.map((practice, index) => (
                <DailyPracticeCard
                  key={practice.id}
                  practice={practice}
                  onPress={() => handlePracticePress(practice)}
                  style={{
                    marginBottom: index < todaysPractices.length - 1 ? 12 : 0,
                  }}
                />
              ))
            ) : (
              <View style={styles.noPracticesCard}>
                <Ionicons name="checkmark-circle" size={48} color="#4a90a4" />
                <Text style={styles.noPracticesTitle}>All Done!</Text>
                <Text style={styles.noPracticesText}>
                  You've completed all practices for today. Great work!
                </Text>
              </View>
            )}
          </View>

          {/* Community Integration */}
          {journey.configuration?.communityIntegration && (
            <View style={styles.communitySection}>
              <Text style={styles.sectionTitle}>Community</Text>
              <CommunityCard
                onPress={handleCommunityPress}
                memberCount={12}
                recentActivity="3 new reflections shared"
              />
            </View>
          )}

          {/* Quick Actions */}
          <View style={styles.actionsSection}>
            <Text style={styles.sectionTitle}>Quick Actions</Text>
            <View style={styles.actionsGrid}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleReflectionPress}
              >
                <Ionicons name="book-outline" size={24} color="#ffffff" />
                <Text style={styles.actionText}>Daily Reflection</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => router.push('/journey/resources' as any)}
              >
                <Ionicons name="library-outline" size={24} color="#ffffff" />
                <Text style={styles.actionText}>Resources</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => router.push('/journey/analytics' as any)}
              >
                <Ionicons name="analytics-outline" size={24} color="#ffffff" />
                <Text style={styles.actionText}>Analytics</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => router.push('/emergency')}
              >
                <Ionicons name="medical-outline" size={24} color="#ffffff" />
                <Text style={styles.actionText}>Support</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#ffffff',
    fontSize: 16,
    marginTop: 16,
    fontFamily: 'Poppins-Regular',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    color: '#ffffff',
    fontSize: 24,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
    fontFamily: 'Poppins-SemiBold',
  },
  errorText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
    fontFamily: 'Poppins-Regular',
  },
  createButton: {
    backgroundColor: '#4a90a4',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 12,
  },
  createButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Poppins-SemiBold',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 24,
  },
  headerContent: {
    flex: 1,
  },
  greeting: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    marginBottom: 4,
    fontFamily: 'Poppins-Regular',
  },
  journeyTitle: {
    color: '#ffffff',
    fontSize: 24,
    fontWeight: '600',
    marginBottom: 4,
    fontFamily: 'Poppins-SemiBold',
  },
  journeySubtitle: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    fontFamily: 'Poppins-Regular',
  },
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  progressSection: {
    marginBottom: 32,
  },
  progressCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  progressContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  progressInfo: {
    marginLeft: 20,
    flex: 1,
  },
  progressTitle: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
    fontFamily: 'Poppins-SemiBold',
  },
  progressPercentage: {
    color: '#4a90a4',
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 2,
    fontFamily: 'Poppins-Bold',
  },
  progressSubtext: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    fontFamily: 'Poppins-Regular',
  },
  statsSection: {
    marginBottom: 32,
  },
  sectionTitle: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
    fontFamily: 'Poppins-SemiBold',
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  practicesSection: {
    marginBottom: 32,
  },
  noPracticesCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
  },
  noPracticesTitle: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
    marginTop: 12,
    marginBottom: 8,
    fontFamily: 'Poppins-SemiBold',
  },
  noPracticesText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    textAlign: 'center',
    fontFamily: 'Poppins-Regular',
  },
  communitySection: {
    marginBottom: 32,
  },
  actionsSection: {
    marginBottom: 32,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    width: (width - 60) / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  actionText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '500',
    marginTop: 8,
    textAlign: 'center',
    fontFamily: 'Poppins-Medium',
  },
});
