/**
 * Assessment Welcome Screen for Feature 1: Understanding Your Inner Landscape (REVISED)
 * Personalized welcome based on user profile from Feature 0
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { colors } from '../../constants/Colors';
import { assessmentService } from '../../services/assessment.service';

interface PersonalizedWelcome {
  userId: string;
  userType: string;
  greeting: string;
  introduction: string;
  explanation: string;
  motivation: string;
  primaryAction: {
    id: string;
    text: string;
    description?: string;
  };
  secondaryActions: {
    id: string;
    text: string;
    description?: string;
  }[];
}

export default function AssessmentWelcomeScreen() {
  const [welcome, setWelcome] = useState<PersonalizedWelcome | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadWelcomeContent();
  }, []);

  const loadWelcomeContent = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get user profile and generate welcome content
      const welcomeData = await assessmentService.getPersonalizedWelcome();
      setWelcome(welcomeData);
    } catch (err) {
      console.error('Error loading welcome content:', err);
      setError('Failed to load welcome content. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleStartAssessment = async () => {
    try {
      setLoading(true);
      const result = await assessmentService.startAssessment();

      // Navigate to assessment flow
      router.push({
        pathname: '/assessment/flow',
        params: { sessionId: result.session.id },
      });
    } catch (err) {
      console.error('Error starting assessment:', err);
      setError('Failed to start assessment. Please try again.');
      setLoading(false);
    }
  };

  const handleSecondaryAction = (actionId: string) => {
    switch (actionId) {
      case 'learn_layers':
        router.push('/assessment/layers-education' as any);
        break;
      case 'emergency_help':
        router.push('/emergency' as any);
        break;
      case 'ruqya_focused':
        router.push('/assessment/ruqya-focused' as any);
        break;
      default:
        console.log('Unknown action:', actionId);
    }
  };

  const renderLoadingState = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={colors.primary} />
      <Text style={styles.loadingText}>
        Preparing your personalized assessment...
      </Text>
    </View>
  );

  const renderErrorState = () => (
    <View style={styles.errorContainer}>
      <Ionicons name="alert-circle-outline" size={48} color={colors.error} />
      <Text style={styles.errorText}>{error}</Text>
      <TouchableOpacity style={styles.retryButton} onPress={loadWelcomeContent}>
        <Text style={styles.retryButtonText}>Try Again</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) return renderLoadingState();
  if (error) return renderErrorState();
  if (!welcome) return renderErrorState();

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#E8F5E8', '#F0F8F0', '#FFFFFF']}
        style={styles.gradient}
      >
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Ionicons name="arrow-back" size={24} color={colors.text} />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>
              Understanding Your Inner Landscape
            </Text>
          </View>

          {/* Welcome Content */}
          <View style={styles.welcomeContainer}>
            {/* Greeting */}
            <Text style={styles.greeting}>{welcome.greeting}</Text>

            {/* Introduction */}
            <Text style={styles.introduction}>{welcome.introduction}</Text>

            {/* Explanation */}
            {welcome.explanation && (
              <View style={styles.explanationContainer}>
                <Text style={styles.explanation}>{welcome.explanation}</Text>
              </View>
            )}

            {/* Motivation */}
            {welcome.motivation && (
              <Text style={styles.motivation}>{welcome.motivation}</Text>
            )}

            {/* Five Layers Preview */}
            <View style={styles.layersPreview}>
              <Text style={styles.layersTitle}>
                The Five Layers of Human Existence:
              </Text>
              <View style={styles.layersList}>
                <View style={styles.layerItem}>
                  <Text style={styles.layerEmoji}>🤲</Text>
                  <Text style={styles.layerText}>Jism (Physical Body)</Text>
                </View>
                <View style={styles.layerItem}>
                  <Text style={styles.layerEmoji}>😤</Text>
                  <Text style={styles.layerText}>Nafs (Ego/Lower Self)</Text>
                </View>
                <View style={styles.layerItem}>
                  <Text style={styles.layerEmoji}>🧠</Text>
                  <Text style={styles.layerText}>Aql (Rational Mind)</Text>
                </View>
                <View style={styles.layerItem}>
                  <Text style={styles.layerEmoji}>💖</Text>
                  <Text style={styles.layerText}>Qalb (Spiritual Heart)</Text>
                </View>
                <View style={styles.layerItem}>
                  <Text style={styles.layerEmoji}>✨</Text>
                  <Text style={styles.layerText}>Ruh (Soul)</Text>
                </View>
              </View>
            </View>

            {/* Assessment Info */}
            <View style={styles.assessmentInfo}>
              <View style={styles.infoItem}>
                <Ionicons
                  name="time-outline"
                  size={20}
                  color={colors.primary}
                />
                <Text style={styles.infoText}>Takes 15-25 minutes</Text>
              </View>
              <View style={styles.infoItem}>
                <Ionicons
                  name="shield-checkmark-outline"
                  size={20}
                  color={colors.primary}
                />
                <Text style={styles.infoText}>Completely private & secure</Text>
              </View>
              <View style={styles.infoItem}>
                <Ionicons
                  name="heart-outline"
                  size={20}
                  color={colors.primary}
                />
                <Text style={styles.infoText}>
                  Personalized spiritual diagnosis
                </Text>
              </View>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.actionsContainer}>
            {/* Primary Action */}
            <TouchableOpacity
              style={styles.primaryButton}
              onPress={handleStartAssessment}
              disabled={loading}
            >
              <LinearGradient
                colors={[colors.primary, colors.primaryDark]}
                style={styles.primaryButtonGradient}
              >
                <Text style={styles.primaryButtonText}>
                  {welcome.primaryAction.text}
                </Text>
                <Ionicons name="arrow-forward" size={20} color="white" />
              </LinearGradient>
            </TouchableOpacity>

            {/* Secondary Actions */}
            {welcome.secondaryActions.map((action, index) => (
              <TouchableOpacity
                key={action.id}
                style={styles.secondaryButton}
                onPress={() => handleSecondaryAction(action.id)}
              >
                <Text style={styles.secondaryButtonText}>{action.text}</Text>
                {action.description && (
                  <Text style={styles.secondaryButtonDescription}>
                    {action.description}
                  </Text>
                )}
              </TouchableOpacity>
            ))}
          </View>

          {/* Crisis Support Notice */}
          <View style={styles.crisisNotice}>
            <Ionicons
              name="information-circle-outline"
              size={16}
              color={colors.textSecondaryy}
            />
            <Text style={styles.crisisNoticeText}>
              If you're in crisis or need immediate help, tap "I Need Immediate
              Help" above or contact emergency services.
            </Text>
          </View>
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  gradient: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    fontSize: 16,
    color: colors.textSecondaryy,
    marginTop: 16,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    fontSize: 16,
    color: colors.error,
    textAlign: 'center',
    marginVertical: 16,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 24,
    paddingTop: 16,
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
    flex: 1,
  },
  welcomeContainer: {
    padding: 24,
  },
  greeting: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.primary,
    marginBottom: 16,
  },
  introduction: {
    fontSize: 16,
    color: colors.text,
    lineHeight: 24,
    marginBottom: 24,
  },
  explanationContainer: {
    backgroundColor: colors.background,
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  explanation: {
    fontSize: 16,
    color: colors.text,
    lineHeight: 24,
  },
  motivation: {
    fontSize: 16,
    color: colors.textSecondaryy,
    fontStyle: 'italic',
    marginBottom: 24,
  },
  layersPreview: {
    backgroundColor: 'white',
    padding: 24,
    borderRadius: 12,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  layersTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 16,
  },
  layersList: {
    gap: 8,
  },
  layerItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  layerEmoji: {
    fontSize: 20,
    marginRight: 16,
  },
  layerText: {
    fontSize: 16,
    color: colors.text,
  },
  assessmentInfo: {
    gap: 8,
    marginBottom: 32,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoText: {
    fontSize: 16,
    color: colors.textSecondaryy,
    marginLeft: 8,
  },
  actionsContainer: {
    padding: 24,
    gap: 16,
  },
  primaryButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  primaryButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    gap: 8,
  },
  primaryButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
  },
  secondaryButton: {
    backgroundColor: 'white',
    padding: 24,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.primary,
    textAlign: 'center',
  },
  secondaryButtonDescription: {
    fontSize: 14,
    color: colors.textSecondaryy,
    textAlign: 'center',
    marginTop: 4,
  },
  crisisNotice: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 24,
    backgroundColor: colors.background,
    margin: 24,
    borderRadius: 8,
  },
  crisisNoticeText: {
    fontSize: 14,
    color: colors.textSecondaryy,
    marginLeft: 8,
    flex: 1,
  },
});
