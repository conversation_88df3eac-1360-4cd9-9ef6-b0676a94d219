import { AntDesign } from '@expo/vector-icons';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useEffect, useLayoutEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import SymptomSelectorContent from '../components/symptoms/SymptomSelectorContent';
import { colors } from '../constants/Colors';
import { SymptomSubmission } from '../services/api/types';

// Define navigation params for type safety
type RootStackParamList = {
  Home: undefined;
  SymptomSelector: {
    initialSymptoms?: Partial<SymptomSubmission>;
    onSubmitRedirect?: string;
  };
  JourneyStartScreen: {
    diagnosis: any;
    symptoms: SymptomSubmission;
  };
  Dashboard: undefined;
};

type SymptomSelectorScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'SymptomSelector'
>;

type SymptomSelectorScreenRouteProp = RouteProp<
  RootStackParamList,
  'SymptomSelector'
>;

export default function SymptomSelectorScreen() {
  const navigation = useNavigation<SymptomSelectorScreenNavigationProp>();
  const route = useRoute<SymptomSelectorScreenRouteProp>();

  // Symptoms context
  // Mock useSymptoms hook
  const symptomsState = { selectedSymptoms: [], categories: [] };
  const fetchSymptoms = async () => console.log('Fetch symptoms');

  // Get initial symptoms from route params if they exist
  const initialSymptoms = route.params?.initialSymptoms;
  const onSubmitRedirect =
    route.params?.onSubmitRedirect || 'JourneyStartScreen';

  // State for loading and error handling
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load symptoms on mount
  useEffect(() => {
    if (symptomsState.categories.length === 0) {
      fetchSymptoms();
    }
  }, []);

  // Configure the header
  useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: 'Symptom Analysis',
      headerTitleAlign: 'center',
      headerStyle: {
        backgroundColor: colors.primary,
      },
      headerTintColor: '#fff',
      headerTitleStyle: {
        fontWeight: 'bold',
      },
      headerLeft: () => (
        <AntDesign
          name="arrowleft"
          size={24}
          color="#fff"
          style={{ marginLeft: 16 }}
          onPress={() => {
            // Show confirmation if user has selected symptoms
            Alert.alert(
              'Leave Symptom Analysis?',
              'Your progress will be lost. Are you sure you want to go back?',
              [
                { text: 'Stay', style: 'cancel' },
                { text: 'Leave', onPress: () => navigation.goBack() },
              ]
            );
          }}
        />
      ),
    });
  }, [navigation]);

  // Handle submission of symptoms
  const handleSubmit = (symptoms: SymptomSubmission, diagnosis: any) => {
    try {
      setLoading(true);

      // Navigate to the appropriate screen based on onSubmitRedirect
      if (onSubmitRedirect === 'JourneyStartScreen') {
        navigation.replace('JourneyStartScreen', {
          symptoms,
          diagnosis,
        });
      } else {
        // Default to dashboard if redirect is not specified
        navigation.replace('Dashboard');
      }
    } catch (err) {
      console.error('Error handling symptom submission:', err);
      setError('Failed to process your symptoms. Please try again.');
      setLoading(false);
    }
  };

  // Handle cancellation
  const handleCancel = () => {
    Alert.alert(
      'Cancel Symptom Analysis?',
      'Your progress will be lost. Are you sure you want to cancel?',
      [
        { text: 'No', style: 'cancel' },
        { text: 'Yes', onPress: () => navigation.goBack() },
      ]
    );
  };

  // Display loading indicator if loading
  if (loading && !error) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Processing your symptoms...</Text>
      </View>
    );
  }

  // Display error message if there's an error
  if (error) {
    return (
      <View style={styles.centerContainer}>
        <AntDesign name="exclamationcircle" size={48} color={colors.error} />
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        <SymptomSelectorContent
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          initialSymptoms={initialSymptoms}
        />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9F9F9',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.error,
    textAlign: 'center',
    marginHorizontal: 32,
  },
});
