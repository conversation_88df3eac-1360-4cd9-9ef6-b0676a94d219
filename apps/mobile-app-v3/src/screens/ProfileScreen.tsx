import React, { useState, useEffect, useCallback } from 'react';

import {
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  RefreshControl,
  Alert,
  TextInput,
  Modal,
  Platform,
  KeyboardAvoidingView,
} from 'react-native';

import { Feather } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import * as Haptics from 'expo-haptics';
import * as ImagePicker from 'expo-image-picker';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { User, UserPreferences } from '../services/api/types';
// Mock functions for now
const getCurrentUser = async (): Promise<User> => ({
  id: 'user-123',
  email: '<EMAIL>',
  created_at: new Date().toISOString(),
  createdAt: new Date().toISOString(),
  preferences: {
    language: 'en',
    notifications: true,
    reminderTimes: ['09:00', '21:00'],
    preferredLayers: [],
  },
});

const updateUserPreferences = async (
  prefs: Partial<UserPreferences>
): Promise<void> => {
  console.log('Updating preferences:', prefs);
};

const updateUserProfile = async (profile: Partial<User>): Promise<void> => {
  console.log('Updating profile:', profile);
};

const changePassword = async (
  oldPassword: string,
  newPassword: string
): Promise<{ success: boolean; message?: string }> => {
  console.log('Changing password');
  return { success: true, message: 'Password changed successfully' };
};

const signOut = async (): Promise<void> => {
  console.log('Signing out');
};
import { SettingsItem } from '../components/SettingsItem';
import { StatsCard } from '../components/StatsCard';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { Text } from '../components/ui/Text';
import { View } from '../components/ui/View';
import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';
// import { useColorScheme from '../hooks/useColorScheme';
// import { RootStackNavigationProp } from '../navigation/types';

export default function ProfileScreen() {
  const navigation = useNavigation<any>();

  const insets = useSafeAreaInsets();

  // State
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Modal states
  const [editNameModalVisible, setEditNameModalVisible] = useState(false);
  const [editEmailModalVisible, setEditEmailModalVisible] = useState(false);
  const [changePasswordModalVisible, setChangePasswordModalVisible] =
    useState(false);

  // Form states
  const [nameInput, setNameInput] = useState('');
  const [emailInput, setEmailInput] = useState('');
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');

  // Load user data
  const loadUserData = useCallback(async () => {
    try {
      setIsLoading(true);
      const userData = await getCurrentUser();
      setUser(userData);
      setNameInput(userData.name || '');
      setEmailInput(userData.email || '');
    } catch (error) {
      console.error('Error loading user data:', error);
      Alert.alert('Error', 'Failed to load profile data. Please try again.');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, []);

  // Load data on mount and when the screen comes into focus
  useEffect(() => {
    loadUserData();
  }, [loadUserData]);

  useFocusEffect(
    React.useCallback(() => {
      loadUserData();
    }, [loadUserData])
  );

  // Handle refresh
  const handleRefresh = () => {
    setIsRefreshing(true);
    loadUserData();
  };

  // Toggle preference
  const togglePreference = async (key: keyof UserPreferences, value: any) => {
    if (!user) {
      return;
    }

    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      // Optimistically update UI
      setUser((prevUser: User | null) => {
        if (!prevUser) {
          return null;
        }

        return {
          ...prevUser,
          preferences: {
            ...prevUser.preferences,
            [key]: value,
          },
        } as User;
      });

      // Update on server
      await updateUserPreferences({ [key]: value });
    } catch (error) {
      console.error(`Error updating ${key}:`, error);

      // Revert on error
      setUser((prevUser: User | null) => {
        if (!prevUser) {
          return null;
        }

        return {
          ...prevUser,
          preferences: {
            ...prevUser.preferences,
            [String(key)]: !value,
          },
        } as User;
      });

      Alert.alert('Error', `Failed to update ${key}. Please try again.`);
    }
  };

  // Handle theme change
  const handleThemeChange = async (theme: 'light' | 'dark' | 'system') => {
    if (!user) {
      return;
    }

    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      // Optimistically update UI
      setUser((prevUser: User | null) => {
        if (!prevUser) {
          return null;
        }

        return {
          ...prevUser,
          preferences: {
            ...prevUser.preferences,
            theme,
          },
        } as User;
      });

      // Update on server
      await updateUserPreferences({ theme });
    } catch (error) {
      console.error('Error updating theme:', error);

      // Revert on error
      setUser((prevUser: User | null) => {
        if (!prevUser) {
          return null;
        }

        return {
          ...prevUser,
          preferences: {
            ...prevUser.preferences,
            theme: user.preferences?.theme,
          },
        } as User;
      });

      Alert.alert('Error', 'Failed to update theme. Please try again.');
    }
  };

  // Handle privacy mode change
  const handlePrivacyModeChange = async (
    mode: 'private' | 'share-with-practitioners' | 'public'
  ) => {
    if (!user) {
      return;
    }

    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      // Optimistically update UI
      setUser((prevUser: User | null) => {
        if (!prevUser) {
          return null;
        }

        return {
          ...prevUser,
          preferences: {
            ...prevUser.preferences,
            privacyMode: mode,
          },
        } as unknown as User;
      });

      // Update on server
      await updateUserPreferences({ privacyMode: mode });
    } catch (error) {
      console.error('Error updating privacy mode:', error);

      // Revert on error
      setUser((prevUser: User | null) => {
        if (!prevUser) {
          return null;
        }

        return {
          ...prevUser,
          preferences: {
            ...prevUser.preferences,
            privacyMode: user.preferences?.privacyMode,
          },
        } as User;
      });

      Alert.alert('Error', 'Failed to update privacy mode. Please try again.');
    }
  };

  // Pick profile image
  const pickProfileImage = async () => {
    if (!user) {
      return;
    }

    try {
      const permissionResult =
        await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (!permissionResult.granted) {
        Alert.alert(
          'Permission Required',
          'Please allow access to your photo library to change your profile picture.'
        );
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const selectedAsset = result.assets[0];

        // In a real app, you would upload the image to a server and get a URL back
        // For this demo, we'll just update the local state with the URI
        setUser((prevUser: User | null) => {
          if (!prevUser) {
            return null;
          }

          return {
            ...prevUser,
            profilePictureUrl: selectedAsset.uri,
          } as User;
        });

        // Update on server (in a real app)
        // await updateUserProfile({ profilePictureUrl: imageUrl });
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert(
        'Error',
        'Failed to update profile picture. Please try again.'
      );
    }
  };

  // Update name
  const handleUpdateName = async () => {
    if (!user || !nameInput.trim()) {
      return;
    }

    try {
      setIsSaving(true);

      await updateUserProfile({ name: nameInput.trim() });

      setUser((prevUser: User | null) => {
        if (!prevUser) {
          return null;
        }

        return {
          ...prevUser,
          name: nameInput.trim(),
        } as User;
      });

      setEditNameModalVisible(false);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      console.error('Error updating name:', error);
      Alert.alert('Error', 'Failed to update name. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  // Update email
  const handleUpdateEmail = async () => {
    if (!user || !emailInput.trim() || !emailInput.includes('@')) {
      return;
    }

    try {
      setIsSaving(true);

      await updateUserProfile({ email: emailInput.trim() });

      setUser((prevUser: User | null) => {
        if (!prevUser) {
          return null;
        }

        return {
          ...prevUser,
          email: emailInput.trim(),
        } as User;
      });

      setEditEmailModalVisible(false);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      console.error('Error updating email:', error);
      Alert.alert('Error', 'Failed to update email. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  // Change password
  const handleChangePassword = async () => {
    if (!currentPassword || !newPassword || !confirmNewPassword) {
      Alert.alert('Error', 'Please fill in all password fields.');
      return;
    }

    if (newPassword !== confirmNewPassword) {
      Alert.alert('Error', 'New passwords do not match.');
      return;
    }

    if (newPassword.length < 8) {
      Alert.alert('Error', 'New password must be at least 8 characters long.');
      return;
    }

    try {
      setIsSaving(true);

      const result = await changePassword(currentPassword, newPassword);

      if (result.success) {
        setChangePasswordModalVisible(false);
        setCurrentPassword('');
        setNewPassword('');
        setConfirmNewPassword('');

        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        Alert.alert('Success', 'Your password has been changed successfully.');
      } else {
        Alert.alert(
          'Error',
          result.message || 'Failed to change password. Please try again.'
        );
      }
    } catch (error) {
      console.error('Error changing password:', error);
      Alert.alert('Error', 'Failed to change password. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  // Handle sign out
  const handleSignOut = async () => {
    Alert.alert('Sign Out', 'Are you sure you want to sign out?', [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Sign Out',
        style: 'destructive',
        onPress: async () => {
          try {
            await signOut();
            // Navigate to auth screen
            navigation.navigate('Auth' as any);
          } catch (error) {
            console.error('Error signing out:', error);
            Alert.alert('Error', 'Failed to sign out. Please try again.');
          }
        },
      },
    ]);
  };

  // Show loading state
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text variant="body" style={styles.loadingText}>
          Loading profile...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        {/* Profile Header */}
        <View style={styles.profileHeader}>
          <TouchableOpacity onPress={pickProfileImage}>
            <View style={styles.profileImageContainer}>
              {user?.profilePictureUrl ? (
                <Image
                  source={{ uri: user.profilePictureUrl }}
                  style={styles.profileImage}
                />
              ) : (
                <View
                  style={[
                    styles.profileImagePlaceholder,
                    { backgroundColor: colors.primary + '30' },
                  ]}
                >
                  <Feather name="user" size={40} color={colors.primary} />
                </View>
              )}
              <View
                style={[
                  styles.editImageButton,
                  { backgroundColor: colors.primary },
                ]}
              >
                <Feather name="camera" size={14} color="#fff" />
              </View>
            </View>
          </TouchableOpacity>

          <View style={styles.profileInfo}>
            <Text variant="heading2">{user?.name}</Text>
            <Text variant="body" color="textSecondary" style={styles.email}>
              {user?.email}
            </Text>
            <Text variant="caption" color="textSecondary">
              Member since{' '}
              {new Date(user?.createdAt || '').toLocaleDateString()}
            </Text>
          </View>

          <Button
            title="Edit Profile"
            variant="outline"
            size="small"
            icon="edit-2"
            onPress={() => setEditNameModalVisible(true)}
            style={styles.editProfileButton}
          />
        </View>

        {/* Stats */}
        {user && (
          <StatsCard
            title="Your Progress"
            stats={[
              {
                label: 'Consecutive Days',
                value: user.stats.consecutiveDays,
                icon: 'calendar',
                color: colors.success,
              },
              {
                label: 'Journal Entries',
                value: user.stats.journalEntries,
                icon: 'book',
                color: colors.qalbGreen,
              },
              {
                label: 'Completed Journeys',
                value: user.stats.completedJourneys,
                icon: 'check-circle',
                color: colors.nafsOrange,
              },
              {
                label: 'Active Journeys',
                value: user.stats.activeJourneys,
                icon: 'compass',
                color: colors.spiritualBlue,
              },
            ]}
          />
        )}

        {/* Account Settings */}
        <View style={styles.settingsSection}>
          <Text variant="subtitle" style={styles.sectionTitle}>
            Account Settings
          </Text>

          <Card style={styles.settingsCard}>
            <SettingsItem
              title="Name"
              description={user?.name}
              icon="user"
              type="navigation"
              onPress={() => setEditNameModalVisible(true)}
            />

            <SettingsItem
              title="Email"
              description={user?.email}
              icon="mail"
              type="navigation"
              onPress={() => setEditEmailModalVisible(true)}
            />

            <SettingsItem
              title="Password"
              description="Change your password"
              icon="lock"
              type="navigation"
              onPress={() => setChangePasswordModalVisible(true)}
            />

            <SettingsItem
              title="Privacy Mode"
              description={
                user?.preferences?.privacyMode === 'private'
                  ? 'Only you can see your data'
                  : user?.preferences?.privacyMode ===
                    'share-with-practitioners'
                  ? 'Shared with healthcare providers'
                  : 'Publicly visible to the community'
              }
              icon="shield"
              type="navigation"
              onPress={() => {
                Alert.alert('Privacy Mode', 'Choose who can see your data', [
                  { text: 'Cancel', style: 'cancel' },
                  {
                    text: 'Private',
                    onPress: () => handlePrivacyModeChange('private'),
                  },
                  {
                    text: 'Practitioners Only',
                    onPress: () =>
                      handlePrivacyModeChange('share-with-practitioners'),
                  },
                  {
                    text: 'Public',
                    onPress: () => handlePrivacyModeChange('public'),
                  },
                ]);
              }}
            />
          </Card>
        </View>

        {/* Notifications */}
        <View style={styles.settingsSection}>
          <Text variant="subtitle" style={styles.sectionTitle}>
            Notifications
          </Text>

          <Card style={styles.settingsCard}>
            <SettingsItem
              title="Push Notifications"
              description="Receive important reminders and updates"
              icon="bell"
              type="toggle"
              value={user?.preferences?.notificationsEnabled}
              onToggle={(value) =>
                togglePreference('notificationsEnabled', value)
              }
            />

            <SettingsItem
              title="Prayer Times"
              description="Get notified before each prayer time"
              icon="clock"
              type="toggle"
              value={user?.preferences?.prayerTimesEnabled}
              onToggle={(value) =>
                togglePreference('prayerTimesEnabled', value)
              }
            />

            <SettingsItem
              title="Journal Reminders"
              description="Daily reminders to write journal entries"
              icon="book"
              type="toggle"
              value={user?.preferences?.journalRemindersEnabled}
              onToggle={(value) =>
                togglePreference('journalRemindersEnabled', value)
              }
            />

            <SettingsItem
              title="Dhikr Reminders"
              description="Reminders to practice daily dhikr"
              icon="repeat"
              type="toggle"
              value={user?.preferences?.dhikrRemindersEnabled}
              onToggle={(value) =>
                togglePreference('dhikrRemindersEnabled', value)
              }
            />
          </Card>
        </View>

        {/* Appearance */}
        <View style={styles.settingsSection}>
          <Text variant="subtitle" style={styles.sectionTitle}>
            Appearance
          </Text>

          <Card style={styles.settingsCard}>
            <SettingsItem
              title="Theme"
              description={
                user?.preferences?.theme === 'system'
                  ? 'Follow system settings'
                  : user?.preferences?.theme === 'light'
                  ? 'Light mode'
                  : 'Dark mode'
              }
              icon="sun"
              type="navigation"
              onPress={() => {
                Alert.alert('Theme', 'Choose your preferred theme', [
                  { text: 'Cancel', style: 'cancel' },
                  { text: 'Light', onPress: () => handleThemeChange('light') },
                  { text: 'Dark', onPress: () => handleThemeChange('dark') },
                  {
                    text: 'System',
                    onPress: () => handleThemeChange('system'),
                  },
                ]);
              }}
            />

            <SettingsItem
              title="Language"
              description={
                user?.preferences?.language === 'en'
                  ? 'English'
                  : user?.preferences?.language
              }
              icon="globe"
              type="navigation"
              onPress={() => {
                Alert.alert(
                  'Language',
                  'This feature is coming soon. Currently only English is supported.',
                  [{ text: 'OK' }]
                );
              }}
            />
          </Card>
        </View>

        {/* Support & About */}
        <View style={styles.settingsSection}>
          <Text variant="subtitle" style={styles.sectionTitle}>
            Support & About
          </Text>

          <Card style={styles.settingsCard}>
            <SettingsItem
              title="Help & Support"
              description="Get help with using the app"
              icon="help-circle"
              type="navigation"
              onPress={() => {
                // Navigate to help screen or open support website
                Alert.alert('Help & Support', 'This feature is coming soon.', [
                  { text: 'OK' },
                ]);
              }}
            />

            <SettingsItem
              title="Privacy Policy"
              description="Read our privacy policy"
              icon="shield"
              type="navigation"
              onPress={() => {
                // Navigate to privacy policy screen or open website
                Alert.alert('Privacy Policy', 'This feature is coming soon.', [
                  { text: 'OK' },
                ]);
              }}
            />

            <SettingsItem
              title="Terms of Service"
              description="Read our terms of service"
              icon="file-text"
              type="navigation"
              onPress={() => {
                // Navigate to terms screen or open website
                Alert.alert(
                  'Terms of Service',
                  'This feature is coming soon.',
                  [{ text: 'OK' }]
                );
              }}
            />

            <SettingsItem
              title="About"
              description="Version 1.0.0"
              icon="info"
              type="navigation"
              onPress={() => {
                // Navigate to about screen
                Alert.alert(
                  'About Qalb Healing',
                  'Version 1.0.0\n\nQalb Healing is designed to help Muslims find inner peace and spiritual connection through authentic Islamic practices.',
                  [{ text: 'OK' }]
                );
              }}
            />
          </Card>
        </View>

        {/* Sign Out */}
        <View style={styles.signOutContainer}>
          <Button
            title="Sign Out"
            variant="outline"
            size="large"
            icon="log-out"
            onPress={handleSignOut}
            style={styles.signOutButton}
          />
        </View>

        {/* Edit Name Modal */}
        <Modal
          visible={editNameModalVisible}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setEditNameModalVisible(false)}
        >
          <KeyboardAvoidingView
            style={{ flex: 1 }}
            behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          >
            <View style={styles.modalOverlay}>
              <Card style={styles.modalCard}>
                <Text variant="subtitle" style={styles.modalTitle}>
                  Edit Name
                </Text>

                <TextInput
                  style={[
                    styles.modalInput,
                    { color: colors.text, borderColor: colors.border },
                  ]}
                  value={nameInput}
                  onChangeText={setNameInput}
                  placeholder="Enter your name"
                  placeholderTextColor={colors.textSecondary}
                />

                <View style={styles.modalButtons}>
                  <Button
                    title="Cancel"
                    variant="outline"
                    size="small"
                    onPress={() => {
                      setNameInput(user?.name || '');
                      setEditNameModalVisible(false);
                    }}
                    style={styles.modalButton}
                  />

                  <Button
                    title="Save"
                    variant="primary"
                    size="small"
                    onPress={handleUpdateName}
                    loading={isSaving}
                    disabled={
                      isSaving || !nameInput.trim() || nameInput === user?.name
                    }
                    style={styles.modalButton}
                  />
                </View>
              </Card>
            </View>
          </KeyboardAvoidingView>
        </Modal>

        {/* Edit Email Modal */}
        <Modal
          visible={editEmailModalVisible}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setEditEmailModalVisible(false)}
        >
          <KeyboardAvoidingView
            style={{ flex: 1 }}
            behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          >
            <View style={styles.modalOverlay}>
              <Card style={styles.modalCard}>
                <Text variant="subtitle" style={styles.modalTitle}>
                  Edit Email
                </Text>

                <TextInput
                  style={[
                    styles.modalInput,
                    { color: colors.text, borderColor: colors.border },
                  ]}
                  value={emailInput}
                  onChangeText={setEmailInput}
                  placeholder="Enter your email"
                  placeholderTextColor={colors.textSecondary}
                  keyboardType="email-address"
                  autoCapitalize="none"
                />

                <View style={styles.modalButtons}>
                  <Button
                    title="Cancel"
                    variant="outline"
                    size="small"
                    onPress={() => {
                      setEmailInput(user?.email || '');
                      setEditEmailModalVisible(false);
                    }}
                    style={styles.modalButton}
                  />

                  <Button
                    title="Save"
                    variant="primary"
                    size="small"
                    onPress={handleUpdateEmail}
                    loading={isSaving}
                    disabled={
                      isSaving ||
                      !emailInput.trim() ||
                      !emailInput.includes('@') ||
                      emailInput === user?.email
                    }
                    style={styles.modalButton}
                  />
                </View>
              </Card>
            </View>
          </KeyboardAvoidingView>
        </Modal>

        {/* Change Password Modal */}
        <Modal
          visible={changePasswordModalVisible}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setChangePasswordModalVisible(false)}
        >
          <KeyboardAvoidingView
            style={{ flex: 1 }}
            behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          >
            <View style={styles.modalOverlay}>
              <Card style={styles.modalCard}>
                <Text variant="subtitle" style={styles.modalTitle}>
                  Change Password
                </Text>

                <TextInput
                  style={[
                    styles.modalInput,
                    { color: colors.text, borderColor: colors.border },
                  ]}
                  value={currentPassword}
                  onChangeText={setCurrentPassword}
                  placeholder="Current password"
                  placeholderTextColor={colors.textSecondary}
                  secureTextEntry
                />

                <TextInput
                  style={[
                    styles.modalInput,
                    { color: colors.text, borderColor: colors.border },
                  ]}
                  value={newPassword}
                  onChangeText={setNewPassword}
                  placeholder="New password"
                  placeholderTextColor={colors.textSecondary}
                  secureTextEntry
                />

                <TextInput
                  style={[
                    styles.modalInput,
                    { color: colors.text, borderColor: colors.border },
                  ]}
                  value={confirmNewPassword}
                  onChangeText={setConfirmNewPassword}
                  placeholder="Confirm new password"
                  placeholderTextColor={colors.textSecondary}
                  secureTextEntry
                />

                <View style={styles.modalButtons}>
                  <Button
                    title="Cancel"
                    variant="outline"
                    size="small"
                    onPress={() => {
                      setCurrentPassword('');
                      setNewPassword('');
                      setConfirmNewPassword('');
                      setChangePasswordModalVisible(false);
                    }}
                    style={styles.modalButton}
                  />

                  <Button
                    title="Save"
                    variant="primary"
                    size="small"
                    onPress={handleChangePassword}
                    loading={isSaving}
                    disabled={
                      isSaving ||
                      !currentPassword ||
                      !newPassword ||
                      !confirmNewPassword ||
                      newPassword !== confirmNewPassword
                    }
                    style={styles.modalButton}
                  />
                </View>
              </Card>
            </View>
          </KeyboardAvoidingView>
        </Modal>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollContent: {
    padding: Theme.spacing.m,
    paddingBottom: Theme.spacing.xl * 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: Theme.spacing.m,
  },
  profileHeader: {
    alignItems: 'center',
    marginBottom: Theme.spacing.l,
  },
  profileImageContainer: {
    position: 'relative',
    marginBottom: Theme.spacing.m,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  profileImagePlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  editImageButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
    ...Theme.shadows.small,
  },
  profileInfo: {
    alignItems: 'center',
  },
  email: {
    marginTop: 4,
    marginBottom: 4,
  },
  editProfileButton: {
    marginTop: Theme.spacing.m,
  },
  settingsSection: {
    marginBottom: Theme.spacing.l,
  },
  sectionTitle: {
    marginBottom: Theme.spacing.s,
  },
  settingsCard: {
    padding: 0,
    overflow: 'hidden',
  },
  signOutContainer: {
    marginTop: Theme.spacing.m,
    marginBottom: Theme.spacing.l,
  },
  signOutButton: {
    width: '100%',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: Theme.spacing.m,
  },
  modalCard: {
    width: '100%',
    maxWidth: 400,
  },
  modalTitle: {
    marginBottom: Theme.spacing.m,
  },
  modalInput: {
    borderWidth: 1,
    borderRadius: Theme.borderRadius.small,
    padding: Theme.spacing.m,
    marginBottom: Theme.spacing.m,
    fontSize: 16,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  modalButton: {
    marginLeft: Theme.spacing.s,
    minWidth: 80,
  },
});
