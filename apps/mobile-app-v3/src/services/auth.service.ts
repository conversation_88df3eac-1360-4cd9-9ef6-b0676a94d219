/**
 * Authentication Service
 * Handles user authentication and profile management
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_BASE_URL } from '../constants/Config';

export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  profile?: UserProfile;
}

export interface UserProfile {
  awarenessLevel?: string;
  ruqyaFamiliarity?: string;
  profession?: string;
  culturalBackground?: string;
  completionStatus?: string;
  recommendedPathway?: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken?: string;
}

class AuthServiceClass {
  private currentUser: User | null = null;
  private authToken: string | null = null;

  /**
   * Initialize auth service and check for existing session
   */
  async initialize(): Promise<void> {
    try {
      const token = await AsyncStorage.getItem('authToken');
      const userData = await AsyncStorage.getItem('userData');

      if (token && userData) {
        this.authToken = token;
        this.currentUser = JSON.parse(userData);
      }
    } catch (error) {
      console.error('Failed to initialize auth service:', error);
    }
  }

  /**
   * Sign in with email and password
   */
  async signIn(email: string, password: string): Promise<AuthResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/signin`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Sign in failed');
      }

      const authData: AuthResponse = await response.json();

      // Store auth data
      await this.storeAuthData(authData);

      return authData;
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    }
  }

  /**
   * Sign up with email and password
   */
  async signUp(
    email: string,
    password: string,
    firstName?: string,
    lastName?: string
  ): Promise<AuthResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/signup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password, firstName, lastName }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Sign up failed');
      }

      const authData: AuthResponse = await response.json();

      // Store auth data
      await this.storeAuthData(authData);

      return authData;
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    }
  }

  /**
   * Sign out current user
   */
  async signOut(): Promise<void> {
    try {
      // Call backend to invalidate token
      if (this.authToken) {
        await fetch(`${API_BASE_URL}/api/auth/signout`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${this.authToken}`,
          },
        });
      }
    } catch (error) {
      console.error('Sign out error:', error);
    } finally {
      // Clear local data regardless of backend response
      await this.clearAuthData();
    }
  }

  /**
   * Get current authenticated user
   */
  getCurrentUser(): User | null {
    return this.currentUser;
  }

  /**
   * Get current auth token
   */
  getAuthToken(): string | null {
    return this.authToken;
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return this.currentUser !== null && this.authToken !== null;
  }

  /**
   * Update user profile
   */
  async updateUserProfile(profileData: Partial<UserProfile>): Promise<void> {
    try {
      if (!this.currentUser) {
        throw new Error('No authenticated user');
      }

      // Update backend
      const response = await fetch(`${API_BASE_URL}/api/auth/profile`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.authToken}`,
        },
        body: JSON.stringify(profileData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Profile update failed');
      }

      // Update local user data
      this.currentUser = {
        ...this.currentUser,
        profile: {
          ...this.currentUser.profile,
          ...profileData,
        },
      };

      // Store updated user data
      await AsyncStorage.setItem('userData', JSON.stringify(this.currentUser));
    } catch (error) {
      console.error('Profile update error:', error);
      throw error;
    }
  }

  /**
   * Refresh auth token
   */
  async refreshToken(): Promise<any> {
    try {
      const refreshToken = await AsyncStorage.getItem('refreshToken');
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken }),
      });

      if (!response.ok) {
        throw new Error('Token refresh failed');
      }

      const result = await response.json();

      this.authToken = result.token;
      await AsyncStorage.setItem('authToken', result.token);

      if (result.refreshToken) {
        await AsyncStorage.setItem('refreshToken', result.refreshToken);
      }

      return result;
    } catch (error) {
      console.error('Token refresh error:', error);
      // If refresh fails, sign out user
      await this.signOut();
      throw error;
    }
  }

  /**
   * Store authentication data locally
   */
  private async storeAuthData(authData: AuthResponse): Promise<void> {
    try {
      this.currentUser = authData.user;
      this.authToken = authData.token;

      await AsyncStorage.setItem('authToken', authData.token);
      await AsyncStorage.setItem('userData', JSON.stringify(authData.user));

      if (authData.refreshToken) {
        await AsyncStorage.setItem('refreshToken', authData.refreshToken);
      }
    } catch (error) {
      console.error('Failed to store auth data:', error);
      throw error;
    }
  }

  /**
   * Clear authentication data
   */
  private async clearAuthData(): Promise<void> {
    try {
      this.currentUser = null;
      this.authToken = null;

      await AsyncStorage.multiRemove(['authToken', 'userData', 'refreshToken']);
    } catch (error) {
      console.error('Failed to clear auth data:', error);
    }
  }

  /**
   * Guest mode - create temporary user for onboarding
   */
  async createGuestUser(): Promise<User> {
    const guestUser: User = {
      id: `guest_${Date.now()}`,
      email: '<EMAIL>',
      firstName: 'Guest',
      profile: {
        completionStatus: 'incomplete',
      },
    };

    this.currentUser = guestUser;
    await AsyncStorage.setItem('userData', JSON.stringify(guestUser));

    return guestUser;
  }

  /**
   * Convert guest user to registered user
   */
  async convertGuestToUser(
    email: string,
    password: string
  ): Promise<AuthResponse> {
    try {
      const guestData = this.currentUser;

      const response = await fetch(`${API_BASE_URL}/api/auth/convert-guest`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
          guestData,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Account creation failed');
      }

      const authData: AuthResponse = await response.json();

      // Store auth data
      await this.storeAuthData(authData);

      return authData;
    } catch (error) {
      console.error('Guest conversion error:', error);
      throw error;
    }
  }

  /**
   * Login user with credentials
   */
  async login(credentials: {
    email: string;
    password: string;
  }): Promise<AuthResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Login failed');
      }

      const authData: AuthResponse = await response.json();

      // Store auth data
      await this.storeAuthData(authData);

      return authData;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  /**
   * Store authentication token securely
   */
  async storeToken(token: string): Promise<void> {
    try {
      this.authToken = token;
      await AsyncStorage.setItem('auth_token', token);
    } catch (error) {
      console.error('Failed to store token:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const authService = new AuthServiceClass();
export default authService;
