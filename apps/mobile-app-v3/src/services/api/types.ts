// API Types for Qalb Healing Mobile App

export type SoulLayer = 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh';

export interface Symptom {
  id: string;
  name: string;
  description: string;
  category: string;
  soulLayer: SoulLayer;
  layer?: SoulLayer; // For backward compatibility
  iconName?: string; // Added missing property
  severity?: number;
  selected?: boolean;
}

export interface SymptomCategory {
  id: string;
  name: string;
  description: string;
  soulLayer: SoulLayer;
  icon: string;
  color: string;
  symptoms: Symptom[];
}

export interface SymptomSubmission {
  jism: string[];
  nafs: string[];
  aql: string[];
  qalb: string[];
  ruh: string[];
  intensity: Record<string, number>;
  duration: string;
  additionalNotes?: string;
}

export interface JournalEntry {
  id: string;
  title: string;
  content: string;
  entryType:
    | 'reflection'
    | 'gratitude'
    | 'general'
    | 'emergency_session'
    | 'challenge';
  category?: string; // Added missing property
  mood?: string;
  emotions: string[];
  tags: string[];
  layers: SoulLayer[];
  createdAt: string;
  updatedAt?: string;
  relatedData?: any;
  isPrivate?: boolean; // Added missing property
  entryDate?: string; // For backward compatibility
}

export interface ApiResponse<T> {
  status: 'success' | 'error';
  data?: T;
  message?: string;
  error?: {
    code: number;
    message: string;
  };
}

export interface User {
  id: string;
  email: string;
  selectedLayers?: SoulLayer[];
  journeyType?: string;
  createdAt?: string;
  created_at?: string; // For backward compatibility
  preferences?: UserPreferences;
  name?: string;
  profilePictureUrl?: string;
  stats?: any; // For backward compatibility
  [key: string]: any; // Allow additional properties
}

export interface AuthResponse {
  user: User;
  session?: {
    access_token: string;
    refresh_token: string;
    expires_at: number;
  };
}

export interface ContentItem {
  id: string;
  title: string;
  description: string;
  content_type: 'audio' | 'video' | 'text' | 'practice';
  category: string;
  healing_layer: SoulLayer;
  duration?: number;
  tags: string[];
  accessUrl?: string;
  created_at: string;
}

export interface Journey {
  id: string;
  journey_type: string;
  focus_layers: SoulLayer[];
  total_days: number;
  current_day: number;
  start_date: string;
  end_date?: string;
  status: 'active' | 'completed' | 'paused';
  progress?: number;
  // Additional properties used in components
  title?: string;
  description?: string;
  duration?: number; // in days
  duration_days?: number; // For backward compatibility
  category?: string;
  targetSoulLayers?: SoulLayer[];
  activities?: Array<{ completed: boolean; [key: string]: any }>;
  imageUrl?: string;
  dailyCommitmentMinutes?: number;
  level?: string;
  user_id?: string; // For backward compatibility
  modules_plan?: any[]; // For backward compatibility
  journey_modules?: any[]; // For backward compatibility
}

export interface JourneyProgress {
  currentDay: number;
  totalDays: number;
  percentage: number;
  status: 'in_progress' | 'completed' | 'paused';
}

export interface EmergencySession {
  id: string;
  trigger_type: 'manual' | 'automatic' | 'scheduled' | 'anxiety';
  current_symptoms: string[];
  start_time: string;
  end_time?: string;
  status: 'active' | 'completed' | 'interrupted';
  recommended_actions: string[];
  estimated_duration: number;
  feedback?: string;
  effectiveness_rating?: number;
  // For backward compatibility
  user_id?: string;
  symptoms?: any;
}

// Add missing types that are referenced in the codebase
export interface SymptomSubmissionResponse {
  success: boolean;
  diagnosis: any;
  recommendations: string[];
  sessionId: string;
  timestamp: string;
  submission?: any; // For backward compatibility
}

export interface SymptomHistoryResponse {
  entries: SymptomEntry[];
  trends: SymptomTrend[];
  insights: string[];
}

export interface SymptomEntry {
  id: string;
  timestamp: string;
  symptoms: SymptomSubmission;
  severity: number;
  notes?: string;
}

export interface SymptomTrend {
  layer: SoulLayer;
  direction: 'improving' | 'worsening' | 'stable';
  confidence: number;
}

export interface LatestDiagnosisResponse {
  id: string;
  timestamp: string;
  primaryLayer: SoulLayer;
  secondaryLayers: SoulLayer[];
  severity: number;
  recommendations: string[];
  followUpDate?: string;
}

export interface SymptomTracking {
  symptomId: string;
  intensity: number;
  timestamp: string;
  notes?: string;
}

export interface SymptomTrackingApiResponse {
  success: boolean;
  trackingId: string;
  insights: string[];
}

export interface JourneyAnalytics {
  totalDays: number;
  completedDays: number;
  streakDays: number;
  averageRating: number;
  layerProgress: Record<SoulLayer, number>;
  insights: string[];
  totalModulesCompleted?: number; // For backward compatibility
  currentStreak?: number; // For backward compatibility
  longestStreak?: number; // For backward compatibility
  completionRate?: number; // For backward compatibility
  lastWeekActivity?: number; // For backward compatibility
}

export interface UserAchievement {
  id: string;
  user_id: string;
  achievement_type: string;
  earned_date: string;
  achievements: {
    id: string;
    name: string;
    description: string;
    icon: string;
  };
}

export interface JourneyProgressDetailed {
  moduleId: string;
  status: string;
  reflections?: string;
  challenges?: string;
  completionDate?: string;
  journeyId?: string; // For backward compatibility
}

export interface JourneyActivity {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  type: string;
}

export interface DailyCheckIn {
  mood: number;
  energy: number;
  spiritualConnection: number;
  notes?: string;
  completedActivities: string[];
  dhikr_count?: number; // For backward compatibility
  prayer_consistency?: number; // For backward compatibility
}

// Removed duplicate JourneyType interface - using type alias below

// Removed duplicate User interface - using the one above

export interface UserProfile {
  id?: string;
  user_id: string;
  email: string;
  name?: string;
  preferences?: UserPreferences;
  selected_layers: SoulLayer[];
  journey_type: string;
  created_at: string;
  updated_at: string;
}

export interface UserPreferences {
  language: string;
  notifications: boolean;
  reminderTimes: string[];
  preferredLayers: SoulLayer[];
  theme?: string; // For backward compatibility
  privacyMode?: boolean | 'private' | 'share-with-practitioners' | 'public'; // For backward compatibility
  notificationsEnabled?: boolean;
  prayerTimesEnabled?: boolean;
  journalRemindersEnabled?: boolean;
  dhikrRemindersEnabled?: boolean;
}

export interface OnboardingResponse {
  session: {
    sessionId: string;
  };
  question: {
    id: string;
    type: string;
    title: string;
    content: string;
  };
  status: 'continue' | 'completed' | 'crisis_detected';
  data?: any;
}

export interface OnboardingSession {
  sessionId: string;
}

export interface OnboardingQuestion {
  id: string;
  type: string;
  title: string;
  content: string;
}

// Emergency types
export type EmergencyTriggerType =
  | 'manual'
  | 'automatic'
  | 'scheduled'
  | 'anxiety';

export interface BreathingPattern {
  inhale: number;
  hold: number;
  exhale: number;
  repetitions: number;
  instructions?: string;
  pattern?: any; // For backward compatibility
  durationSeconds?: number;
  visualGuide?: string;
  audioGuide?: string;
}

export interface DhikrContent {
  arabic: string;
  transliteration: string;
  translation: string;
  count: number;
  benefits?: string[];
  primary?: any; // For backward compatibility
  secondary?: any;
  tertiary?: any;
  audio?: string;
}

export interface RuqyahVerse {
  id?: string;
  arabic: string;
  transliteration?: string;
  translation: string;
  reference?: string;
  audioUrl?: string;
  surah?: string; // For backward compatibility
}

// Additional missing types for endpoints
export interface AuthSession {
  access_token: string;
  refresh_token: string;
  expires_at: number;
}

export interface Diagnosis {
  id: string;
  primaryLayer: SoulLayer;
  secondaryLayers: SoulLayer[];
  severity: number;
  recommendations: string[];
  timestamp: string;
}

export interface JourneyModule {
  id: string;
  title: string;
  description: string;
  content: any;
  duration: number;
}

export interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export type EntryType =
  | 'reflection'
  | 'gratitude'
  | 'general'
  | 'emergency_session'
  | 'challenge';

export interface ReflectionPrompt {
  id: string;
  title?: string;
  content?: string;
  text?: string; // Alternative to content
  category: string;
  layer?: SoulLayer;
  tags?: string[];
  focus_area?: string[];
  display_order?: number;
  status?: string;
}

export interface JournalAnalytics {
  totalEntries: number;
  entriesThisWeek: number;
  averageMood: number;
  topTags: string[];
  layerDistribution: Record<SoulLayer, number>;
}

export type ContentType = 'audio' | 'video' | 'text' | 'practice';

export interface ContentCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
}

export interface ContentInteraction {
  contentId: string;
  type: 'view' | 'like' | 'share' | 'complete';
  duration?: number;
  rating?: number;
}

// Journey types
export type JourneyType = '7-day' | '14-day' | '21-day' | '40-day' | 'custom';

// DailyCheckInLegacy interface for backward compatibility
export interface DailyCheckInLegacy {
  mood: string;
  dhikr_count: number;
  prayer_consistency: number;
  notes?: string;
  energy: number;
  spiritualConnection: number;
  completedActivities: string[];
}

// Additional types for dummyData
export interface SymptomHistory {
  history: Array<{
    id: number;
    user_id: string;
    jism_symptoms: string[];
    nafs_symptoms: string[];
    aql_symptoms: string[];
    qalb_symptoms: string[];
    ruh_symptoms: string[];
    submission_date: string;
    user_diagnoses: {
      layers_affected: string[];
      spotlight: string;
      recommended_journey: string;
    };
  }>;
}

export interface LatestDiagnosis {
  diagnosis: {
    id: number;
    user_id: string;
    submission_id: number;
    layers_affected: string[];
    spotlight: string;
    recommended_journey: string;
    severity_level: string;
    diagnosis_date: string;
    symptom_submissions: {
      jism_symptoms: string[];
      nafs_symptoms: string[];
      intensity_ratings: {
        jism: number;
        nafs: number;
      };
    };
  };
}

export interface SymptomTrackingResponse {
  tracking: {
    id: number;
    user_id: string;
    symptom_id: number;
    intensity: number;
    notes: string;
    tracking_date: string;
  };
}
